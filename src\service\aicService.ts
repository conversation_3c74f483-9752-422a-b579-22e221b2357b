import http from '@/axios'
import type { ICommonResponse } from '@/types/axios'
import type {
    IAicConditionData,
    IAicConditionDataRequest,
    IAicNormalSearchRules,
    ISearchListParams,
    ModelGetCategoryListResponse,
    AddProjectParams,
    SaveTempleteParams,
    UpdateTemplateParams,
    GsGetPersonEnterpriseRelationsParams,
    GsGetPersonEnterpriseRelationsResponse,
    ICenterEntParams,
    ICenterEntResponse,
} from '@/types/aic'
import type {
    ISearchGSInfoParams,
    IGetModelCategoryResponse,
    IGetCompanyByCodeParams,
    SearchCompanyGsInfoResponse,
    CompanyBaseInfo,
    GetCompanyDataParams,
    GetCompanyContactResponse,
    ISearchAdvancedSearchParams,
    ISearchAdvancedSearchResponse,
    ISearchGetCategoryParams,
    ISearchGetCategoryResponse,
    ISearchGetTemplateParams,
    ISearchGetTemplateResponse,
    ISearchBidAndFactoryParams,
    ISearchBidAndFactoryResponse,
    ISearchBidProjectResponse,
    ISearchBidCompanyResponse,
} from '@/types/company'
import type { IPageUserItem } from '@/types/user'
import type { IHighSearchRules } from '@/types/model'
export default {
    conditionGetData(body?: IAicConditionDataRequest): Promise<IAicConditionData> {
        return http.get(`/api/zhenqi-aic/condition/get-data`, {
            params: body,
        })
    },
    conditionGetUser(): Promise<IPageUserItem[]> {
        return http.get(`/api/zhenqi-aic/condition/get-user`)
    },
    searchEnterprise(data: IGetCompanyByCodeParams): Promise<ISearchAdvancedSearchResponse> {
        return http.post(`/api/zhenqi-aic/search/enterprise`, data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    gsInfo(data: ISearchGSInfoParams): Promise<SearchCompanyGsInfoResponse> {
        return http.get(`/api/zhenqi-aic/gs/info`, {
            params: data,
        })
    },
    gsGetCompanyBaseInfo(data: GetCompanyDataParams): Promise<CompanyBaseInfo> {
        return http.get(`/api/zhenqi-aic/gs/get-company-base-info`, {
            params: data,
        })
    },
    gsGetContacts(data: GetCompanyDataParams): Promise<GetCompanyContactResponse> {
        return http.get(`/api/zhenqi-aic/gs/get-contacts`, {
            params: data,
        })
    },
    conditionGetDetailModel(): Promise<IGetModelCategoryResponse[]> {
        return http.get(`/api/zhenqi-aic/condition/get-detail-model`)
    },
    conditionGetInfo(body: { searchType: string }): Promise<Array<IHighSearchRules>> {
        return http.get(`/api/zhenqi-aic/condition/get-info`, {
            params: body,
        })
    },
    conditionGetInfoForNomal(body: { searchType: string }): Promise<IAicNormalSearchRules[]> {
        return http.get(`/api/zhenqi-aic/condition/get-info`, {
            params: body,
        })
    },
    searchAdvancedSearch(data: ISearchAdvancedSearchParams): Promise<ISearchAdvancedSearchResponse> {
        return http.post(`/api/zhenqi-aic/search/advanced-search`, data, {
            hideError: true,
        })
    },

    searchGetTemplate(data: ISearchGetTemplateParams): Promise<ISearchGetTemplateResponse> {
        return http.get(`/api/zhenqi-aic/search/get-template`, {
            params: data,
            hideError: true,
        })
    },
    searchFactory(data: ISearchBidAndFactoryParams): Promise<ISearchBidAndFactoryResponse> {
        return http.post('/api/zhenqi-aic/search/factory', data, {
            hideError: true,
        })
    },
    searchTenderProject(data: ISearchBidAndFactoryParams): Promise<ISearchBidProjectResponse> {
        return http.post('/api/zhenqi-aic/search/tender-project', data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    searchScene(data: ISearchBidAndFactoryParams): Promise<ISearchBidCompanyResponse> {
        return http.post('/api/zhenqi-aic/search/scene', data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    modelGetCategoryList(data: ISearchListParams): Promise<ModelGetCategoryListResponse> {
        return http.get(`/api/zhenqi-aic/model/get-category-list`, {
            params: data,
        })
    },
    modelGetCategory(data: ISearchGetCategoryParams): Promise<ISearchGetCategoryResponse> {
        return http.get(`/api/zhenqi-aic/model/get-category`, {
            params: data,
            hideError: true,
        })
    },
    // 新增项目分类
    modelNewCategory(data: AddProjectParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-aic/model/new-category', data, {
            hideError: true,
        })
    },
    // 修改项目分类节点信息
    modelUpdateCategory(data: AddProjectParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-aic/model/update-category', data, {
            hideError: true,
        })
    },
    // 删除项目分类
    modelDeleteTopCategory(categoryId: string): Promise<ICommonResponse> {
        return http.delete('/api/zhenqi-aic/model/delete-top-category', {
            params: { categoryId },
            hideError: true,
        })
    },
    // 删除节点
    modelDeleteCategory(categoryId: string): Promise<ICommonResponse> {
        return http.delete('/api/zhenqi-aic/model/delete-category', {
            params: { categoryId },
            hideError: true,
        })
    },
    // 赋值项目
    modelCopyCategory(categoryId: string): Promise<ICommonResponse> {
        return http.post(
            '/api/zhenqi-aic/model/copy-category',
            { categoryId: categoryId },
            {
                hideError: true,
            }
        )
    },
    searchDeleteTemplate(templateId: string): Promise<ICommonResponse> {
        return http.delete('/api/zhenqi-aic/search/delete-template', {
            params: { templateId },
        })
    },
    modelLoadCategory(data: { categoryId?: string }): Promise<ISearchGetCategoryResponse> {
        return http.get(`/api/zhenqi-aic/model/load-category`, {
            params: data,
            hideError: true,
        })
    },
    searchSaveTemplate(data: SaveTempleteParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-aic/search/save-template', data)
    },
    searchUpdateTemplate(data: UpdateTemplateParams): Promise<ICommonResponse> {
        return http.put('/api/zhenqi-aic/search/update-template', data)
    },
    gsGetPersonEnterpriseRelations(
        data: GsGetPersonEnterpriseRelationsParams
    ): Promise<GsGetPersonEnterpriseRelationsResponse> {
        return http.get('/api/zhenqi-aic/gs/get-person-enterprise-relations', {
            params: data,
            hideError: true,
        })
    },
    gsGetGetAppointDetailModel(data: { name: string }): Promise<{ data: IGetModelCategoryResponse }> {
        return http.get('/api/zhenqi-aic/condition/get-appoint-detail-model', {
            params: data,
            hideError: true,
        })
    },
    searchCenterEnt(data: ICenterEntParams): Promise<ICenterEntResponse> {
        return http.post('/api/zhenqi-aic/search/center-ent', data, {
            hideError: true,
        })
    },
    searchStatistics(data: ICenterEntParams): Promise<ICenterEntResponse> {
        return http.post('/api/zhenqi-aic/search/statistics', data, {
            hideError: true,
        })
    },
    // 获取招投标详情
    searchGetTenderContents(data: { ossId: string }): Promise<string> {
        return http.get(`/api/zhenqi-aic/search/get-tender-contents`, {
            params: data,
        })
    },
    searchLastEnt(data: IGetCompanyByCodeParams): Promise<ISearchAdvancedSearchResponse> {
        return http.post(`/api/zhenqi-aic/search/last-ent`, data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    // 获取年报详情
    getAnnualReportDetail(data:{annualReportId:string}): Promise<string> {
        return http.get(`/api/zhenqi-aic/gs/get-annual-report-detail`, {
            params: data,
        })
    },
}
